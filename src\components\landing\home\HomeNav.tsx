"use client";
import { buttonVariants } from "@/components/ui/button";
import { cn, getRedirectUrl } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import NavigationSheet from "./NavigationSheet";

const HomeNav = ({ env }: { env?: string }) => {
  const pathname = usePathname();
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isScrollUp, setIsScrollUp] = useState(false);
  const navItems = [
    { label: "Home", href: "/", type: "scroll" },
    {
      label: "Product",
      href: pathname === "/" ? "#learning-revolution" : "/#learning-revolution",
      type: "scroll",
    },
    { label: "Blog", href: "/blog", type: "link" },
    { label: "Contact us", href: "/contact", type: "link" },
    {
      label: "Testimonials",
      href: pathname === "/" ? "#testimonial" : "/#testimonial",
      type: "scroll",
    },
  ];

  const smoothScrollToElement = (targetId: string) => {
    const element = document.getElementById(targetId);
    if (element) {
      const navHeight = 96;
      const elementPosition =
        element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - navHeight;
      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth",
      });
    }
  };

  const extractTargetId = (href: string): string => {
    return href.startsWith("/#") ? href.slice(2) : href.slice(1);
  };

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const navHeight = 120;
      const scrollThreshold = 0;

      const scrollDifference = currentScrollY - lastScrollY;
      const isScrollingUp = scrollDifference < 0;
      const isScrollingDown = scrollDifference > 0;

      const isInStickyZone = currentScrollY > navHeight;

      if (Math.abs(scrollDifference) > scrollThreshold)
        if (isScrollingUp && !isScrollUp) setIsScrollUp(true);
        else if (isScrollingDown && isScrollUp) setIsScrollUp(false);

      if (isInStickyZone) {
        if (!isVisible) setIsVisible(true);
      } else if (isVisible) {
        setIsVisible(false);
        setIsScrollUp(false);
      }

      setLastScrollY(currentScrollY);
    };

    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", throttledHandleScroll, { passive: true });

    return () => window.removeEventListener("scroll", throttledHandleScroll);
  }, [lastScrollY, isScrollUp, isVisible]);

  const navigationClickHandler = (
    event: React.MouseEvent<HTMLAnchorElement>,
    item: (typeof navItems)[0],
  ) => {
    if (item.type === "scroll") {
      event.preventDefault();
      const targetId = extractTargetId(item.href);

      if (pathname !== "/") {
        router.push("/");

        let hasScrolled = false;

        const attemptScroll = () => {
          if (hasScrolled) return;

          const element = document.getElementById(targetId);
          if (element) {
            smoothScrollToElement(targetId);
            hasScrolled = true;
          } else setTimeout(attemptScroll, 50);
        };

        setTimeout(attemptScroll, 100);
      } else smoothScrollToElement(targetId);
    }
  };

  return (
    <>
      {/* Main nav */}
      <div
        className={cn(
          "flex h-20 w-full flex-row-reverse items-center justify-between bg-background px-[7.5%] py-3.5 md:h-24 lg:w-[85%] lg:flex-row lg:justify-around lg:px-0",
          "fixed top-0 z-50",
          "lg:static lg:z-auto",
        )}
      >
        <div className="size-6  lg:hidden" />
        <div className="flex h-full justify-start">
          <Image
            src="/assets/icons/parhlai_white_purple.svg"
            alt="icon"
            width={0}
            height={0}
            style={{ objectFit: "contain", width: "auto", height: "100%" }}
          />
        </div>
        <div className="ml-9 hidden w-1/2 grow justify-around lg:flex xl:justify-start xl:space-x-4">
          {navItems?.map((item, index) => (
            <Link
              key={index}
              className={buttonVariants({ variant: "link" })}
              href={item.href}
              onClick={(event) => navigationClickHandler(event, item)}
            >
              {item.label}
            </Link>
          ))}
        </div>
        <div className="hidden h-full flex-auto items-center justify-end space-x-8 lg:flex">
          {/* Aggregate Calculator */}
          <Link
            className={cn(
              buttonVariants({ variant: "default", size: "login" }),
              "w-auto min-w-max px-6",
            )}
            href="/aggregate-calculator"
          >
            Aggregate Calculator
          </Link>
          <Link
            className={cn(
              buttonVariants({ variant: "default", size: "login" }),
              "",
            )}
            href={getRedirectUrl("register", env)}
          >
            Register
          </Link>
          <Link
            className={cn(
              buttonVariants({ variant: "default", size: "login" }),
              "bg-accent-3",
            )}
            href={getRedirectUrl("login", env)}
          >
            Sign in
          </Link>
        </div>
        <NavigationSheet env={env} />
      </div>

      {/* Sticky nav on scroll up */}
      <div
        className={cn(
          "fixed top-0 z-50 h-24 w-full flex-row items-center justify-around bg-background/90 px-[7.5%] py-3.5 shadow-lg backdrop-blur-md",
          "hidden",
          "lg:flex",
          "transition-all duration-300 ease-out",
          isVisible && isScrollUp ? "translate-y-0" : "-translate-y-full",
        )}
      >
        <div className="size-6  lg:hidden" />
        <div className="flex h-full justify-start">
          <Image
            src="/assets/icons/parhlai_white_purple.svg"
            alt="icon"
            width={0}
            height={0}
            style={{ objectFit: "contain", width: "auto", height: "100%" }}
          />
        </div>
        <div className="ml-9 hidden w-1/2 grow justify-around lg:flex xl:justify-start xl:space-x-4">
          {navItems?.map((item, index) => (
            <Link
              key={index}
              className={buttonVariants({ variant: "link" })}
              href={item.href}
              onClick={(event) => navigationClickHandler(event, item)}
            >
              {item.label}
            </Link>
          ))}
        </div>
        <div className="hidden h-full flex-auto items-center justify-end space-x-8 lg:flex">
          {/* Aggregate calculator*/}
          <Link
            className={cn(
              buttonVariants({ variant: "default", size: "login" }),
              "w-auto min-w-max px-6",
            )}
            href="/aggregate-calculator"
          >
            Aggregate Calculator
          </Link>
          <Link
            className={cn(
              buttonVariants({ variant: "default", size: "login" }),
              "",
            )}
            href={getRedirectUrl("register", env)}
          >
            Register
          </Link>
          <Link
            className={cn(
              buttonVariants({ variant: "default", size: "login" }),
              "bg-accent-3",
            )}
            href={getRedirectUrl("login", env)}
          >
            Sign in
          </Link>
        </div>
        <NavigationSheet env={env} />
      </div>
    </>
  );
};

export default HomeNav;
