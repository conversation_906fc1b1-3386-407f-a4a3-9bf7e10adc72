"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  CardContent,
  Card<PERSON>eader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import Results from "@/components/landing/aggregate/Results";
import type {
  Grade,
  University,
  EducationSystem,
  ActiveTab,
  OLevelGrades,
  ALevelGrades,
  ScoreData,
} from "@/types/aggregate-calculator";

const AggregateCalculatorContent = (): JSX.Element => {
  const [selectedUniversity, setSelectedUniversity] =
    useState<University>("NUST");
  const [selectedSystem, setSelectedSystem] =
    useState<EducationSystem>("O / A Levels");
  const [activeTab, setActiveTab] = useState<ActiveTab>("calculator");
  const [showResults, setShowResults] = useState<boolean>(false);

  const [oLevelGrades, setOLevelGrades] = useState<OLevelGrades>({
    subject1: "",
    subject2: "",
    subject3: "",
    subject4: "",
    subject5: "",
    subject6: "",
    subject7: "",
    subject8: "",
  });

  const [aLevelGrades, setALevelGrades] = useState<ALevelGrades>({
    subject1: "",
    subject2: "",
    subject3: "",
    subject4: "",
    subject5: "",
    subject6: "",
    subject7: "",
    subject8: "",
  });

  //  state variables for subject names
  const [oLevelSubjectNames, setOLevelSubjectNames] = useState<{
    [key in keyof OLevelGrades]: string;
  }>({
    subject1: "",
    subject2: "",
    subject3: "",
    subject4: "",
    subject5: "",
    subject6: "",
    subject7: "",
    subject8: "",
  });

  const [aLevelSubjectNames, setALevelSubjectNames] = useState<{
    [key in keyof ALevelGrades]: string;
  }>({
    subject1: "",
    subject2: "",
    subject3: "",
    subject4: "",
    subject5: "",
    subject6: "",
    subject7: "",
    subject8: "",
  });

  const [entryTestScore, setEntryTestScore] = useState<string>("");
  const [includeALevels, setIncludeALevels] = useState<boolean>(false);

  const gradeOptions: Grade[] = ["A*", "A", "B", "C", "D", "E"];

  const handleOLevelGradeChange = (
    subject: keyof OLevelGrades,
    grade: Grade,
  ): void => {
    setOLevelGrades((prev) => ({
      ...prev,
      [subject]: grade,
    }));
  };

  const handleALevelGradeChange = (
    subject: keyof ALevelGrades,
    grade: Grade,
  ): void => {
    setALevelGrades((prev) => ({
      ...prev,
      [subject]: grade,
    }));
  };

  const [matricScores, setMatricScores] = useState<ScoreData>({
    obtained: "",
    total: "",
  });
  const [fscScores, setFscScores] = useState<ScoreData>({
    obtained: "",
    total: "",
  });

  const calculateAggregate = (): void => {
    // Validate that required fields are filled
    if (!entryTestScore) {
      alert("Please enter your Entry Test Score");
      return;
    }

    if (selectedSystem === "Matric & FSC") {
      if (
        !matricScores.obtained ||
        !matricScores.total ||
        !fscScores.obtained ||
        !fscScores.total
      ) {
        alert("Please enter all Matric and FSC scores");
        return;
      }
    } else {
      // Check if at least some O-Level grades are entered
      const hasOLevelGrades = Object.values(oLevelGrades).some(
        (grade) => grade !== "",
      );
      if (!hasOLevelGrades) {
        alert("Please enter at least one O-Level grade");
        return;
      }
    }

    // Show results
    setShowResults(true);
    console.log("Calculating aggregate...");
  };
  const handleCalculateAgain = (): void => {
    setShowResults(false);
  };

  const reset = (): void => {
    setOLevelGrades({
      subject1: "",
      subject2: "",
      subject3: "",
      subject4: "",
      subject5: "",
      subject6: "",
      subject7: "",
      subject8: "",
    });
    setALevelGrades({
      subject1: "",
      subject2: "",
      subject3: "",
      subject4: "",
      subject5: "",
      subject6: "",
      subject7: "",
      subject8: "",
    });
    setOLevelSubjectNames({
      subject1: "",
      subject2: "",
      subject3: "",
      subject4: "",
      subject5: "",
      subject6: "",
      subject7: "",
      subject8: "",
    });
    setALevelSubjectNames({
      subject1: "",
      subject2: "",
      subject3: "",
      subject4: "",
      subject5: "",
      subject6: "",
      subject7: "",
      subject8: "",
    });
    setEntryTestScore("");
    setIncludeALevels(false);
    setMatricScores({
      obtained: "",
      total: "",
    });
    setFscScores({
      obtained: "",
      total: "",
    });
  };

  if (showResults)
    return (
      <Results
        selectedSystem={selectedSystem}
        matricScores={matricScores}
        fscScores={fscScores}
        oLevelGrades={oLevelGrades}
        aLevelGrades={aLevelGrades}
        includeALevels={includeALevels}
        entryTestScore={entryTestScore}
        onCalculateAgain={handleCalculateAgain}
      />
    );

  const renderOALevelsForm = (): JSX.Element => (
    <div className="space-y-6">
      {/* O Level Grades Section */}
      <div>
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-medium">O Level Grades</h3>
        </div>

        <div className="grid grid-cols-2 gap-3 sm:gap-4 md:grid-cols-4">
          {(Object.keys(oLevelGrades) as Array<keyof OLevelGrades>).map(
            (subject) => (
              <div key={subject}>
                {/* Add Subject Button or Input */}
                {oLevelSubjectNames[subject] === "" ? (
                  <button
                    type="button"
                    onClick={() =>
                      setOLevelSubjectNames((prev) => ({
                        ...prev,
                        [subject]: " ",
                      }))
                    }
                    className="mb-2 text-sm font-medium text-[#7c6bb5] hover:underline"
                    style={{ fontFamily: "Poppins, sans-serif" }}
                  >
                    + Add Your Subject
                  </button>
                ) : (
                  <input
                    type="text"
                    placeholder="Enter Subject"
                    value={oLevelSubjectNames[subject].trim()}
                    onChange={(e) =>
                      setOLevelSubjectNames((prev) => ({
                        ...prev,
                        [subject]: e.target.value,
                      }))
                    }
                    className="focus:ring-purple-500 mb-2 w-full rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                    style={{
                      fontFamily: "Poppins, sans-serif",
                      color: "#515151",
                    }}
                    autoFocus
                  />
                )}

                {/* Grade Dropdown */}
                <select
                  value={oLevelGrades[subject]}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                    handleOLevelGradeChange(subject, e.target.value as Grade)
                  }
                  className="focus:ring-purple-500 w-full rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                >
                  <option value="">-</option>
                  {gradeOptions.map((grade) => (
                    <option key={grade} value={grade}>
                      {grade}
                    </option>
                  ))}
                </select>
              </div>
            ),
          )}
        </div>
      </div>

      {/* Include A-Levels Checkbox */}
      <div className="flex items-center gap-2">
        <input
          type="checkbox"
          id="includeALevels"
          checked={includeALevels}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setIncludeALevels(e.target.checked)
          }
          className="text-purple-600 focus:ring-purple-500 h-4 w-4 rounded border-gray-300 bg-gray-100"
        />
        <label htmlFor="includeALevels" className="text-sm text-gray-700">
          Include A - Levels in the merit calculation
        </label>
      </div>

      {/* A Level Grades Section */}
      {includeALevels && (
        <div>
          <h3 className="mb-4 text-lg font-medium">A Level Grades</h3>
          <div className="grid grid-cols-1 gap-3 sm:gap-4 md:grid-cols-3">
            {(Object.keys(aLevelGrades) as Array<keyof ALevelGrades>).map(
              (subject) => (
                <div key={subject}>
                  {/* Add Subject Button or Input */}
                  {aLevelSubjectNames[subject] === "" ? (
                    <button
                      type="button"
                      onClick={() =>
                        setALevelSubjectNames((prev) => ({
                          ...prev,
                          [subject]: " ",
                        }))
                      }
                      className="mb-2 text-sm font-medium text-[#7c6bb5] hover:underline"
                      style={{ fontFamily: "Poppins, sans-serif" }}
                    >
                      + Add Your Subject
                    </button>
                  ) : (
                    <input
                      type="text"
                      placeholder="Enter Subject"
                      value={aLevelSubjectNames[subject].trim()}
                      onChange={(e) =>
                        setALevelSubjectNames((prev) => ({
                          ...prev,
                          [subject]: e.target.value,
                        }))
                      }
                      className="focus:ring-purple-500 mb-2 w-full rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                      style={{
                        fontFamily: "Poppins, sans-serif",
                        color: "#515151",
                      }}
                      autoFocus
                    />
                  )}

                  {/* Grade Dropdown */}
                  <select
                    value={aLevelGrades[subject]}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                      handleALevelGradeChange(subject, e.target.value as Grade)
                    }
                    className="focus:ring-purple-500 w-full rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                  >
                    <option value="">-</option>
                    {gradeOptions.map((grade) => (
                      <option key={grade} value={grade}>
                        {grade}
                      </option>
                    ))}
                  </select>
                </div>
              ),
            )}
          </div>
        </div>
      )}
    </div>
  );

  const renderMatricFSCForm = (): JSX.Element => (
    <div className="w-full space-y-6">
      {/* Desktop: Side by side, Mobile: Stacked */}
      <div className="flex flex-col space-y-6 lg:flex-row lg:gap-8 lg:space-y-0">
        {/* Matric Scores */}
        <div className="flex-1">
          <div className="space-y-3">
            <h3 className="text-lg font-medium text-gray-900">Matric</h3>
            <p className="text-sm text-gray-600">Enter your Matric Scores</p>

            {/* Desktop: Inline layout, Mobile: Stacked with labels */}
            <div className="space-y-3 sm:flex sm:items-center sm:gap-3 sm:space-y-0">
              {/* Mobile labels (hidden on desktop) */}
              <div className="flex gap-2 sm:hidden">
                <div className="flex-1">
                  <label className="mb-1 block text-xs text-gray-500">
                    Marks
                  </label>
                  <input
                    type="number"
                    value={matricScores.obtained}
                    onChange={(e) =>
                      setMatricScores((prev) => ({
                        ...prev,
                        obtained: e.target.value,
                      }))
                    }
                    placeholder="-"
                    className="focus:ring-purple-500 w-full rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                  />
                </div>
                <div className="flex items-center justify-center px-2 pt-6">
                  <span className="text-gray-500">/</span>
                </div>
                <div className="flex-1">
                  <label className="mb-1 block text-xs text-gray-500">
                    Total
                  </label>
                  <input
                    type="number"
                    value={matricScores.total}
                    onChange={(e) =>
                      setMatricScores((prev) => ({
                        ...prev,
                        total: e.target.value,
                      }))
                    }
                    placeholder="-"
                    className="focus:ring-purple-500 w-full rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                  />
                </div>
              </div>

              {/* Desktop layout (hidden on mobile) */}
              <div className="hidden sm:flex sm:items-center sm:gap-3">
                <input
                  type="number"
                  value={matricScores.obtained}
                  onChange={(e) =>
                    setMatricScores((prev) => ({
                      ...prev,
                      obtained: e.target.value,
                    }))
                  }
                  placeholder="-"
                  className="focus:ring-purple-500 w-20 rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                />
                <span className="text-gray-500">out of</span>
                <input
                  type="number"
                  value={matricScores.total}
                  onChange={(e) =>
                    setMatricScores((prev) => ({
                      ...prev,
                      total: e.target.value,
                    }))
                  }
                  placeholder="-"
                  className="focus:ring-purple-500 w-20 rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                />
              </div>
            </div>
          </div>
        </div>

        {/* FSC Scores */}
        <div className="flex-1">
          <div className="space-y-3">
            <h3 className="text-lg font-medium text-gray-900">FSc</h3>
            <p className="text-sm text-gray-600">Enter your FSc Scores</p>

            {/* Desktop: Inline layout, Mobile: Stacked with labels */}
            <div className="space-y-3 sm:flex sm:items-center sm:gap-3 sm:space-y-0">
              {/* Mobile labels (hidden on desktop) */}
              <div className="flex gap-2 sm:hidden">
                <div className="flex-1">
                  <label className="mb-1 block text-xs text-gray-500">
                    Marks
                  </label>
                  <input
                    type="number"
                    value={fscScores.obtained}
                    onChange={(e) =>
                      setFscScores((prev) => ({
                        ...prev,
                        obtained: e.target.value,
                      }))
                    }
                    placeholder="-"
                    className="focus:ring-purple-500 w-full rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                  />
                </div>
                <div className="flex items-center justify-center px-2 pt-6">
                  <span className="text-gray-500">/</span>
                </div>
                <div className="flex-1">
                  <label className="mb-1 block text-xs text-gray-500">
                    Total
                  </label>
                  <input
                    type="number"
                    value={fscScores.total}
                    onChange={(e) =>
                      setFscScores((prev) => ({
                        ...prev,
                        total: e.target.value,
                      }))
                    }
                    placeholder="-"
                    className="focus:ring-purple-500 w-full rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                  />
                </div>
              </div>

              {/* Desktop layout (hidden on mobile) */}
              <div className="hidden sm:flex sm:items-center sm:gap-3">
                <input
                  type="number"
                  value={fscScores.obtained}
                  onChange={(e) =>
                    setFscScores((prev) => ({
                      ...prev,
                      obtained: e.target.value,
                    }))
                  }
                  placeholder="-"
                  className="focus:ring-purple-500 w-20 rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                />
                <span className="text-gray-500">out of</span>
                <input
                  type="number"
                  value={fscScores.total}
                  onChange={(e) =>
                    setFscScores((prev) => ({
                      ...prev,
                      total: e.target.value,
                    }))
                  }
                  placeholder="-"
                  className="focus:ring-purple-500 w-20 rounded-md border border-gray-300 p-2 focus:border-transparent focus:outline-none focus:ring-2"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMeritInformation = (): JSX.Element => (
    <div
      style={{
        fontFamily: "Poppins, sans-serif",
        fontWeight: 200,
        fontSize: "16px",
        fontStyle: "normal",
        color: "#515151",
      }}
    >
      <div className="space-y-6">
        <div>
          <h3
            className="mb-4 text-xl font-medium"
            style={{ color: "#515151", fontWeight: "normal" }}
          >
            Merit Information — How NUST Calculates Your Aggregate & Ranks You
          </h3>
        </div>

        <div>
          <h4
            className="mb-3 text-lg font-medium"
            style={{ color: "#515151", fontWeight: "normal" }}
          >
            1. NUST Aggregate (Merit Score) Calculation
          </h4>
          <p className="mb-4">
            NUST computes the aggregate using a weighted formula based on your
            academic background:
          </p>
          <ul className="ml-4 space-y-2">
            <li>• NET (NUST Entry Test) — 75 %</li>
            <li>• SSC or O-level (or equivalent) — 10 %</li>
            <li>• HSSC (FSc) or A-level (or equivalent) — 15 %</li>
            <li className="ml-4">
              ◦ However, if you are an A-level student still awaiting final
              A-level results, then 25 % weightage is assigned to your O-level
              equivalence marks as per IBCC equivalence.
            </li>
          </ul>
        </div>

        <div>
          <p className="mb-2">
            <strong>Formula for FSc / HSSC Students:</strong>
          </p>
          <p className="mb-4">
            Aggregate = (SSC% × 0.10) + (HSSC% × 0.15) + (NET% × 0.75)
          </p>

          <p className="mb-2">
            <strong>
              Formula for O/A-level Students (awaiting final results):
            </strong>
          </p>
          <p className="mb-4">
            Aggregate = (O-level equivalence × 0.25) + (NET% × 0.75)
          </p>
        </div>

        <div>
          <h4
            className="mb-3 text-lg font-medium"
            style={{ color: "#515151", fontWeight: "normal" }}
          >
            2. Eligibility Requirements
          </h4>
          <p className="mb-4">
            To apply for undergraduate admission at NUST, candidates must have
            secured at least 60 % marks each in SSC and HSSC (or their
            equivalent), as per IBCC equivalence guidelines.
          </p>
        </div>

        <div>
          <h4
            className="mb-3 text-lg font-medium"
            style={{ color: "#515151", fontWeight: "normal" }}
          >
            3. Interpreting Your Aggregate — Merit Position & Admission Chances
          </h4>
          <p className="mb-4">
            NUST does not publish merit lists or cutoff data. Instead, closing
            merit positions vary annually by program and campus.
          </p>
          <p className="mb-4">
            FSc (HSSC) students aggregate is composed of 10 % Matric, 15 % HSSC,
            and 75 % NET, while O/A-level candidates (with results pending) rely
            on 25 % O-level equivalence plus 75 % NET.
          </p>
          <p className="mb-4">
            While merit thresholds shift, typical patterns may include:
          </p>
          <ul className="ml-4 space-y-2">
            <li>
              • 80 + %: Strong likelihood of admission to top programs and
              campus priority.
            </li>
            <li>
              • 75–79 %: Competitive for engineering, computing and popular
              programs.
            </li>
            <li>
              • 70–74 %: Moderate chances—may require strategic program
              selection.
            </li>
            <li>
              • Below 70 %: Admission possible mainly in less in-demand programs
              or through later rounds.
            </li>
          </ul>
        </div>

        <div>
          <h4
            className="mb-3 text-lg font-medium"
            style={{ color: "#515151", fontWeight: "normal" }}
          >
            4. Summary — Quick Reference
          </h4>
          <ul className="ml-4 space-y-2">
            <li>
              • Eligibility: Minimum 60 % marks in both SSC and HSSC or
              equivalent.
            </li>
            <li>
              • Aggregate Formula:
              <ul className="ml-4 mt-2 space-y-1">
                <li>
                  ◦ FSc/HSSC Students: SSC (10 %) + HSSC (15 %) + NET (75 %)
                </li>
                <li>
                  ◦ O/A-level Students (awaiting results): O-level equivalent
                  (25 %) + NET (75 %)
                </li>
              </ul>
            </li>
            <li>
              • Admission Insight:
              <ul className="ml-4 mt-2 space-y-1">
                <li>◦ 80+ %: Strong applicant in top campuses</li>
                <li>◦ 75–79 % — Strong contender</li>
                <li>◦ 70–74 % — Mixed chances, varies by program</li>
                <li>◦ Below 70 % — More competitive, lower likelihood</li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );

  return (
    <div className="mx-auto mt-8 max-w-4xl px-3 sm:mt-10 sm:px-4">
      <Card className="shadow-lg">
        <CardHeader className="pb-3 text-center sm:pb-4">
          <CardTitle
            className="mb-2 text-2xl font-semibold sm:text-3xl md:text-4xl"
            style={{ color: "#5936cd" }}
          >
            Aggregate Calculator
          </CardTitle>

          <CardDescription className="text-sm text-gray-600 sm:text-base">
            Calculate your aggregate based on universities official formulas!
          </CardDescription>

          {/* Tabs */}
          <div className="mt-4 flex justify-center">
            <div className="flex flex-wrap items-center justify-center gap-2 rounded-lg bg-gray-100 p-1 sm:gap-0">
              <button
                onClick={() => setActiveTab("calculator")}
                className={`rounded-md px-4 py-2 text-sm font-medium transition-colors sm:px-6 ${
                  activeTab === "calculator"
                    ? "text-white shadow-sm"
                    : "hover:opacity-80"
                }`}
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontSize: "16px",
                  fontWeight: 400,
                  backgroundColor:
                    activeTab === "calculator" ? "#5936cd" : "transparent",
                  color: activeTab === "calculator" ? "white" : "#5936cd",
                }}
              >
                Aggregate Calculator
              </button>

              <button
                onClick={() => setActiveTab("merit")}
                className={`rounded-md px-4 py-2 text-sm font-medium transition-colors sm:px-6 ${
                  activeTab === "merit"
                    ? "text-white shadow-sm"
                    : "hover:opacity-80"
                }`}
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontSize: "16px",
                  fontWeight: 400,
                  backgroundColor:
                    activeTab === "merit" ? "#5936cd" : "transparent",
                  color: activeTab === "merit" ? "white" : "#5936cd",
                }}
              >
                Merit Information
              </button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6 sm:space-y-8">
          {activeTab === "calculator" ? (
            <>
              {/* University Selection */}
              <div>
                <label className="mb-2 block text-sm font-medium">
                  University / Entry Test
                </label>
                <select
                  value={selectedUniversity}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                    setSelectedUniversity(e.target.value as University)
                  }
                  className="focus:ring-purple-500 w-full rounded-md border border-gray-300 p-3 focus:border-transparent focus:outline-none focus:ring-2"
                >
                  <option value="NUST">NUST</option>
                </select>
              </div>

              {/* Education System Selection */}
              <div>
                <label className="mb-2 block text-sm font-medium">
                  Education System
                </label>
                <select
                  value={selectedSystem}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                    setSelectedSystem(e.target.value as EducationSystem)
                  }
                  className="focus:ring-purple-500 w-full rounded-md border border-gray-300 p-3 focus:border-transparent focus:outline-none focus:ring-2"
                >
                  <option value="O / A Levels">O / A Levels</option>
                  <option value="Matric & FSC">Matric & FSC</option>
                </select>
              </div>

              {/* Dynamic Form */}
              {selectedSystem === "O / A Levels"
                ? renderOALevelsForm()
                : renderMatricFSCForm()}

              {/* Entry Test Score */}
              <div>
                <h3 className="mb-2 text-lg font-medium">Entry Test Score</h3>
                <p className="mb-3 text-sm text-gray-600">
                  Enter the score you got in the Entry Test
                </p>
                <input
                  type="number"
                  value={entryTestScore}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setEntryTestScore(e.target.value)
                  }
                  placeholder="XXX"
                  className="focus:ring-purple-500 w-full rounded-md border border-gray-300 p-3 focus:border-transparent focus:outline-none focus:ring-2 sm:w-40"
                />
              </div>

              {/* Action Buttons */}
              <div className="mx-auto flex w-full max-w-lg flex-col gap-3 pt-4 sm:flex-row sm:items-center sm:justify-between sm:pt-6">
                <button
                  onClick={reset}
                  className="w-full rounded-md bg-gray-400 px-6 py-3 font-medium text-white transition-colors hover:bg-gray-500 sm:w-[200px]"
                >
                  Reset
                </button>

                <button
                  onClick={calculateAggregate}
                  className="w-full rounded-md px-6 py-3 font-medium text-white transition-colors hover:opacity-90 sm:w-[220px]"
                  style={{ backgroundColor: "#5936cd" }}
                >
                  Calculate Aggregate
                </button>
              </div>
            </>
          ) : (
            renderMeritInformation()
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AggregateCalculatorContent;
