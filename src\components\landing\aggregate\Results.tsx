import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import type {
  Grade,
  EducationSystem,
  OLevelGrades,
  ALevelGrades,
  ScoreData,
} from "@/types/aggregate-calculator";

type ResultsProps = {
  selectedSystem: EducationSystem;
  matricScores?: ScoreData;
  fscScores?: ScoreData;
  oLevelGrades?: OLevelGrades;
  aLevelGrades?: ALevelGrades;
  includeALevels?: boolean;
  entryTestScore: string;
  onCalculateAgain: () => void;
};

const Results: React.FC<ResultsProps> = ({
  selectedSystem,
  matricScores,
  fscScores,
  oLevelGrades,
  aLevelGrades,
  includeALevels,
  entryTestScore,
  onCalculateAgain,
}) => {
  const [activeTab, setActiveTab] = useState<"summary" | "comparison">(
    "summary",
  );
  const [aggregateScore, setAggregateScore] = useState<number>(0);

  // IBCC Subject-specific A* equivalence tables
  const oLevelAStarEquivalence: { [subject: string]: number } = {
    physics: 95,
    chemistry: 94,
    biology: 94,
    "computer studies": 94,
    mathematics: 94,
    english: 93,
    urdu: 93,
    "pakistan studies": 93,
    islamiyat: 94,
    default: 94,
  };

  const aLevelAStarEquivalence: { [subject: string]: number } = {
    physics: 95,
    chemistry: 94,
    biology: 94,
    "computer studies": 93,
    mathematics: 94,
    default: 94,
  };

  // Helpers
  const getSubjectSpecificPercentage = (
    grade: Grade,
    subject: string,
    level: "olevel" | "alevel",
  ): number => {
    if (grade === "A*") {
      const equivalenceTable =
        level === "olevel" ? oLevelAStarEquivalence : aLevelAStarEquivalence;

      const subjectKey = subject.toLowerCase();
      return equivalenceTable[subjectKey] || equivalenceTable.default;
    }

    const gradeMap: { [key in Grade]: number } = {
      "A*": 95, // not used because of the condition above
      A: 85,
      B: 75,
      C: 65,
      D: 55,
      E: 45,
      "": 0,
    };
    return gradeMap[grade] || 0;
  };

  const calculateOLevelEquivalence = (): number => {
    if (!oLevelGrades) return 0;

    const subjects = Object.keys(oLevelGrades);
    const validSubjects = subjects.filter(
      (subject) => oLevelGrades[subject as keyof OLevelGrades] !== "",
    );

    if (validSubjects.length === 0) return 0;

    const total = validSubjects.reduce((sum, subject) => {
      const grade = oLevelGrades[subject as keyof OLevelGrades] as Grade;
      return sum + getSubjectSpecificPercentage(grade, subject, "olevel");
    }, 0);

    return total / validSubjects.length;
  };

  const calculateALevelEquivalence = (): number => {
    if (!aLevelGrades || !includeALevels) return 0;

    const subjects = Object.keys(aLevelGrades);
    const validSubjects = subjects.filter(
      (subject) => aLevelGrades[subject as keyof ALevelGrades] !== "",
    );

    if (validSubjects.length === 0) return 0;

    const total = validSubjects.reduce((sum, subject) => {
      const grade = aLevelGrades[subject as keyof ALevelGrades] as Grade;
      return sum + getSubjectSpecificPercentage(grade, subject, "alevel");
    }, 0);

    return total / validSubjects.length;
  };

  const getMatricPercentage = (): number => {
    if (!matricScores?.obtained || !matricScores?.total) return 0;
    const obtained = parseFloat(matricScores.obtained);
    const total = parseFloat(matricScores.total);
    return total > 0 ? (obtained / total) * 100 : 0;
  };

  const getFscPercentage = (): number => {
    if (!fscScores?.obtained || !fscScores?.total) return 0;
    const obtained = parseFloat(fscScores.obtained);
    const total = parseFloat(fscScores.total);
    return total > 0 ? (obtained / total) * 100 : 0;
  };

  const getOtherComponentsPercentage = (): number => {
    if (selectedSystem === "Matric & FSC")
      return getMatricPercentage() * 0.1 + getFscPercentage() * 0.15;

    if (includeALevels && aLevelGrades) {
      const validALevelSubjects = Object.keys(aLevelGrades).filter(
        (subject) => aLevelGrades[subject as keyof ALevelGrades] !== "",
      );

      if (validALevelSubjects.length > 0)
        return (
          calculateOLevelEquivalence() * 0.1 +
          calculateALevelEquivalence() * 0.15
        );
    }

    return calculateOLevelEquivalence() * 0.25;
  };

  // NET % from marks (out of 200)
  const netMarksToPercent = (marks: number): number =>
    Math.max(0, Math.min(100, (marks / 200) * 100));

  const getRequiredNetMarks = (targetAggregate: number) => {
    const other = getOtherComponentsPercentage();
    const netWeight = 0.75;
    const requiredNetPercent = (targetAggregate - other) / netWeight;

    if (requiredNetPercent <= 0)
      return {
        status: "already" as const,
        requiredMarks: 0,
        maxAchievable: other + netWeight * 100,
      };

    if (requiredNetPercent > 100)
      return {
        status: "impossible" as const,
        requiredMarks: 200,
        maxAchievable: other + netWeight * 100,
      };

    const requiredMarks = Math.ceil((requiredNetPercent / 100) * 200);
    return {
      status: "ok" as const,
      requiredMarks,
      maxAchievable: other + netWeight * 100,
    };
  };

  const getCalculationMethod = (): string => {
    if (selectedSystem === "Matric & FSC")
      return "10% Matriculation + 15% Intermediate + 75% NET";

    if (includeALevels && aLevelGrades) {
      const validALevelSubjects = Object.keys(aLevelGrades).filter(
        (subject) => aLevelGrades[subject as keyof ALevelGrades] !== "",
      );
      if (validALevelSubjects.length > 0)
        return "10% O-Level equivalence (IBCC-based) + 15% A-Level equivalence (IBCC-based) + 75% NET";
    }

    return "25% O-Level equivalence (IBCC-based) + 75% NET (A-Level results awaiting)";
  };

  // Aggregate calculation
  useEffect(() => {
    const netScore = parseFloat(entryTestScore) || 0; // out of 200
    const netPercentage = netMarksToPercent(netScore);

    let aggregate = 0;

    if (selectedSystem === "Matric & FSC")
      aggregate =
        getMatricPercentage() * 0.1 +
        getFscPercentage() * 0.15 +
        netPercentage * 0.75;
    else {
      const oPct = calculateOLevelEquivalence();
      const aPct = calculateALevelEquivalence();

      const hasALevelScores =
        includeALevels &&
        aLevelGrades &&
        Object.keys(aLevelGrades).some(
          (subject) => aLevelGrades[subject as keyof ALevelGrades] !== "",
        );

      if (hasALevelScores)
        aggregate = oPct * 0.1 + aPct * 0.15 + netPercentage * 0.75;
      else aggregate = oPct * 0.25 + netPercentage * 0.75;
    }

    setAggregateScore(aggregate);
  }, [
    selectedSystem,
    matricScores,
    fscScores,
    oLevelGrades,
    aLevelGrades,
    includeALevels,
    entryTestScore,
  ]);

  const targetAggregate = 85;
  const improvementInfo = getRequiredNetMarks(targetAggregate);
  const currentNetMarks = Number.isFinite(parseFloat(entryTestScore))
    ? Math.max(0, Math.min(200, parseFloat(entryTestScore)))
    : 0;

  const getAdmissionChance = (): "High" | "Medium" | "Low" => {
    if (aggregateScore >= 80) return "High";
    if (aggregateScore >= 60) return "Medium";
    return "Low";
  };

  const getChanceColor = (chance: string): string => {
    switch (chance) {
      case "High":
        return "text-green-600";
      case "Medium":
        return "text-yellow-600";
      default:
        return "text-red-600";
    }
  };

  const getProgressBarWidth = (chance: string): string => {
    switch (chance) {
      case "High":
        return "w-4/5";
      case "Medium":
        return "w-3/5";
      default:
        return "w-2/5";
    }
  };

  const getProgressBarColor = (chance: string): string => {
    switch (chance) {
      case "High":
        return "bg-green-500";
      case "Medium":
        return "bg-yellow-500";
      default:
        return "bg-red-500";
    }
  };

  // NUST Programs with merit requirements
  const engineeringPrograms = [
    { name: "Software Engineering", merit: 88, color: "#5936cd" },
    { name: "Computer Science", merit: 86, color: "#5936cd" },
    { name: "Electrical Engineering", merit: 85, color: "#5936cd" },
    { name: "Mechanical Engineering", merit: 84, color: "#5936cd" },
    { name: "Civil Engineering", merit: 83, color: "#5936cd" },
    { name: "Aerospace Engineering", merit: 87, color: "#5936cd" },
  ];

  const businessPrograms = [
    { name: "Business Administration", merit: 78, color: "#5936cd" },
    { name: "Economics", merit: 76, color: "#5936cd" },
    { name: "Finance", merit: 79, color: "#5936cd" },
    { name: "Marketing", merit: 74, color: "#5936cd" },
    { name: "Management Sciences", merit: 77, color: "#5936cd" },
    { name: "International Relations", merit: 75, color: "#5936cd" },
  ];

  const getBarWidth = (merit: number): string => {
    const percentage = Math.min((merit / 100) * 100, 100);
    return `${percentage}%`;
  };

  const getScoreDisplay = (merit: number): string => merit.toString();

  return (
    <div className="mx-auto mt-8 max-w-4xl px-3 sm:mt-10 sm:px-4">
      <Card className="rounded-lg shadow-xl">
        <CardHeader className="rounded-md pb-3 text-center sm:pb-4">
          <CardTitle
            className="font-poppins mb-2 text-2xl font-semibold sm:mb-3 sm:text-3xl md:text-4xl"
            style={{ color: "#5936cd" }}
          >
            Your University Merit Results
          </CardTitle>
          <p className="mb-5 text-sm text-gray-600 sm:mb-6 sm:text-base">
            Based on official weightage formulas
          </p>

          {/* Tab Navigation */}
          <div className="flex justify-center">
            <div className="flex flex-wrap items-center justify-center gap-2 rounded-lg bg-gray-100 p-1 sm:gap-0">
              <button
                onClick={() => setActiveTab("summary")}
                className={`rounded-md px-4 py-2 text-sm font-medium transition-colors sm:px-6 ${
                  activeTab === "summary"
                    ? "text-white shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
                style={{
                  backgroundColor:
                    activeTab === "summary" ? "#5936cd" : "transparent",
                }}
              >
                Summary
              </button>
              <button
                onClick={() => setActiveTab("comparison")}
                className={`rounded-md px-4 py-2 text-sm font-medium transition-colors sm:px-6 ${
                  activeTab === "comparison"
                    ? "text-white shadow-lg"
                    : "text-gray-600 hover:text-gray-900"
                }`}
                style={{
                  backgroundColor:
                    activeTab === "comparison" ? "#5936cd" : "transparent",
                }}
              >
                Program Comparison
              </button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6 sm:space-y-8">
          {activeTab === "summary" ? (
            <>
              {/* Aggregate Score Display */}
              <div className="py-4 text-center sm:py-6">
                <h3 className="mb-1 text-base font-medium sm:mb-2 sm:text-lg">
                  Your Aggregate Score
                </h3>
                <div
                  className="mb-1 text-3xl font-bold sm:mb-2 sm:text-4xl md:text-5xl"
                  style={{ color: "#5936cd" }}
                >
                  {aggregateScore.toFixed(2)}%
                </div>
              </div>

              {/* Admission Chances */}
              <div>
                <CardTitle
                  className="font-poppins mb-3 text-xl font-semibold sm:mb-4 sm:text-2xl md:text-3xl"
                  style={{ color: "#5936cd" }}
                >
                  Admission Chances
                </CardTitle>
                <div className="grid grid-cols-1 gap-4 sm:gap-5 md:grid-cols-2">
                  {[
                    "Engineering",
                    "Medical",
                    "Business",
                    "Social Sciences",
                  ].map((field) => (
                    <div key={field}>
                      <div className="mb-2 flex items-center justify-between">
                        <span className="text-sm font-medium sm:text-base">
                          {field}
                        </span>
                        <span
                          className={`text-sm font-medium sm:text-base ${getChanceColor(
                            getAdmissionChance(),
                          )}`}
                        >
                          {getAdmissionChance()}
                        </span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-gray-200">
                        <div
                          className={`h-2 rounded-full ${getProgressBarColor(
                            getAdmissionChance(),
                          )} ${getProgressBarWidth(getAdmissionChance())}`}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Improvement Opportunity */}
              {aggregateScore < targetAggregate && (
                <div
                  className="border-purple-200 rounded-lg border p-3 sm:p-4"
                  style={{ backgroundColor: "#E9E3FF" }}
                >
                  <div className="mb-2 flex items-center gap-2">
                    <div className="bg-purple-500 flex h-4 w-4 items-center justify-center rounded-full">
                      <span className="text-xs text-white">!</span>
                    </div>
                    <h4 className="text-purple-900 font-medium">
                      Improvement Opportunity
                    </h4>
                  </div>

                  {improvementInfo.status === "ok" && (
                    <p className="text-purple-700 text-xs sm:text-sm">
                      To reach an aggregate of <b>{targetAggregate}%</b>, you
                      should aim for at least{" "}
                      <b>{improvementInfo.requiredMarks}</b> marks in the NET
                      (out of 200).
                      {currentNetMarks > 0 &&
                      improvementInfo.requiredMarks > currentNetMarks
                        ? ` That's ${
                            improvementInfo.requiredMarks - currentNetMarks
                          } more marks than your current NET score of ${currentNetMarks}.`
                        : ""}
                    </p>
                  )}

                  {improvementInfo.status === "already" && (
                    <p className="text-purple-700 text-xs sm:text-sm">
                      You are already at or above the target aggregate of{" "}
                      <b>{targetAggregate}%</b>. Keep it up!
                    </p>
                  )}

                  {improvementInfo.status === "impossible" && (
                    <p className="text-purple-700 text-xs sm:text-sm">
                      Even a perfect NET score (200/200) can only get you to{" "}
                      <b>{improvementInfo.maxAchievable.toFixed(2)}%</b>.
                      Consider improving your O/A-Level or Matric/FSc
                      components, or targeting programs with lower closing
                      merit.
                    </p>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col gap-3 pt-4 sm:flex-row sm:gap-0 sm:space-x-4 sm:pt-6">
                <button
                  onClick={onCalculateAgain}
                  className="hover:bg-purple-50 w-full rounded-md border-2 px-6 py-3 text-center font-medium transition-colors"
                  style={{ borderColor: "#5936cd", color: "#5936cd" }}
                >
                  Calculate Again
                </button>
              </div>

              {/* Summary */}
              <div className="border-t pt-4 sm:pt-6">
                <h4 className="mb-2 text-sm font-medium sm:text-base">
                  Summary:
                </h4>
                <p className="text-xs leading-relaxed text-gray-600 sm:text-sm">
                  Based on the {selectedSystem} system, your aggregate score of{" "}
                  {aggregateScore.toFixed(2)}% has been calculated using NUST
                  official formula. The calculation includes{" "}
                  {getCalculationMethod()}. This score determines your admission
                  chances across different programs at the university.
                </p>
              </div>
            </>
          ) : (
            <div className="space-y-5 sm:space-y-6">
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                <h3
                  className="font-poppins text-xl font-semibold sm:text-2xl md:text-3xl"
                  style={{ color: "#5936cd" }}
                >
                  Program Comparison
                </h3>
                <span
                  className="text-xs sm:text-sm"
                  style={{ color: "#707070" }}
                >
                  2025 Closing Merit
                </span>
              </div>

              {/* Program Comparison Section */}
              <Card className="rounded-lg border border-gray-200 bg-white p-4 shadow-lg sm:p-6">
                {/* Card Header */}
                <CardHeader className="mb-4 p-0 sm:mb-6">
                  <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                    <span className="font-poppins text-base font-semibold text-[#5936cd] sm:text-lg md:text-xl">
                      Program Merit Comparison (2025)
                    </span>
                    <span className="text-xs text-[#707070] sm:text-sm">
                      Your Aggregate: {aggregateScore.toFixed(0)}%
                    </span>
                  </div>
                </CardHeader>

                {/* Engineering and Computing Section */}
                <CardContent className="p-0">
                  <h4
                    className="font-poppins mb-3 text-lg font-semibold sm:mb-4 sm:text-xl"
                    style={{ color: "#5936cd" }}
                  >
                    Engineering and Computing
                  </h4>
                  <div className="space-y-3 sm:space-y-4">
                    {engineeringPrograms.map((program, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-3 sm:gap-4"
                      >
                        <div className="flex h-4 w-4 items-center justify-center rounded-full border-2 border-gray-300">
                          <div className="h-2 w-2 rounded-full bg-gray-300"></div>
                        </div>
                        <div className="flex-1">
                          <div className="mb-1 flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-700">
                              {program.name}
                            </span>
                            <span className="text-sm font-medium text-orange-500">
                              {getScoreDisplay(program.merit)}
                            </span>
                          </div>
                          <div className="h-2 w-full rounded-full bg-gray-200">
                            <div
                              className="h-2 rounded-full transition-all duration-300"
                              style={{
                                width: getBarWidth(program.merit),
                                backgroundColor: program.color,
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>

                {/* Business and Social Sciences Section */}
                <CardContent className="mt-5 p-0 sm:mt-6">
                  <h4
                    className="font-poppins mb-3 text-lg font-semibold sm:mb-4 sm:text-xl"
                    style={{ color: "#5936cd" }}
                  >
                    Business and Social Sciences
                  </h4>
                  <div className="space-y-3 sm:space-y-4">
                    {businessPrograms.map((program, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-3 sm:gap-4"
                      >
                        <div className="flex h-4 w-4 items-center justify-center rounded-full border-2 border-gray-300">
                          <div className="h-2 w-2 rounded-full bg-gray-300"></div>
                        </div>
                        <div className="flex-1">
                          <div className="mb-1 flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-700">
                              {program.name}
                            </span>
                            <span className="text-sm font-medium text-orange-500">
                              {getScoreDisplay(program.merit)}
                            </span>
                          </div>
                          <div className="h-2 w-full rounded-full bg-gray-200">
                            <div
                              className="h-2 rounded-full transition-all duration-300"
                              style={{
                                width: getBarWidth(program.merit),
                                backgroundColor: program.color,
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Results;
