import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import DOMPurify from "dompurify";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const getRedirectUrl = (action: "login" | "register", env?: string) => {
  return env === "production"
    ? `https://app.parhlai.com/account/${action}`
    : env === "preview"
      ? `https://app-preview.parhlai.com/account/${action}`
      : `/register`;
};

export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
    .trim()
    .replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
};

export const stripHtml = (html: string): string => {
  // Basic way to strip HTML
  const div = document.createElement("div");
  div.innerHTML = DOMPurify.sanitize(html); // sanitize to prevent XSS
  return div.textContent || div.innerText || "";
};
